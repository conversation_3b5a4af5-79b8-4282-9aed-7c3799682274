#!/usr/bin/env python3
"""
Core Bayesian trial functionality without GUI
Demonstrates the trajectory_demo functionality in command line
"""

import numpy as np
import time

class BayesianTrialCore:
    """Core Bayesian trial calculations without GUI dependencies"""
    
    def __init__(self, block=20, n_max=120, thr_eff=0.95, thr_fut=0.05, mc_samp=1000):
        self.BLOCK = block
        self.N_MAX = n_max
        self.THR_EFF = thr_eff
        self.THR_FUT = thr_fut
        self.MC_SAMP = mc_samp
        
        # Trial data
        self.reset_trial_data()
        
    def reset_trial_data(self):
        """Reset all trial data to initial state"""
        self.xT = 0  # Treatment successes
        self.nT = 0  # Treatment total
        self.xC = 0  # Control successes
        self.nC = 0  # Control total
        self.ppos_history = []
        self.batch_number = 0
        self.trial_active = True
        self.decision_history = []
        
    def post_sup(self, aT, bT, aC, bC, S=None):
        """
        Calculate posterior superiority probability P(pT > pC | data)
        using Beta-Binomial conjugate prior with Monte Carlo simulation
        """
        if S is None:
            S = self.MC_SAMP
            
        # Sample from Beta posterior distributions
        pT_samples = np.random.beta(aT, bT, S)
        pC_samples = np.random.beta(aC, bC, S)
        
        # Calculate proportion where pT > pC
        return np.mean(pT_samples > pC_samples)
        
    def calculate_ppos(self, xT, nT, xC, nC):
        """Calculate Predictive Probability of Success (PPoS)"""
        if nT == 0 or nC == 0:
            return 0.5  # No data yet
            
        # Remaining patients per group
        remaining_T = self.N_MAX - nT
        remaining_C = self.N_MAX - nC
        
        if remaining_T <= 0 or remaining_C <= 0:
            # Already at maximum, calculate final posterior
            aT = xT + 1  # Beta prior: alpha = 1
            bT = nT - xT + 1  # Beta prior: beta = 1
            aC = xC + 1
            bC = nC - xC + 1
            return self.post_sup(aT, bT, aC, bC)
            
        # Monte Carlo simulation for future outcomes
        num_sims = 1000
        success_count = 0
        
        for _ in range(num_sims):
            # Current posterior parameters
            aT_curr = xT + 1
            bT_curr = nT - xT + 1
            aC_curr = xC + 1
            bC_curr = nC - xC + 1
            
            # Sample current success rates from posterior
            pT_curr = np.random.beta(aT_curr, bT_curr)
            pC_curr = np.random.beta(aC_curr, bC_curr)
            
            # Simulate future outcomes
            future_xT = np.random.binomial(remaining_T, pT_curr)
            future_xC = np.random.binomial(remaining_C, pC_curr)
            
            # Final counts
            final_xT = xT + future_xT
            final_nT = nT + remaining_T
            final_xC = xC + future_xC
            final_nC = nC + remaining_C
            
            # Final posterior parameters
            aT_final = final_xT + 1
            bT_final = final_nT - final_xT + 1
            aC_final = final_xC + 1
            bC_final = final_nC - final_xC + 1
            
            # Check if this simulation leads to success
            final_post_sup = self.post_sup(aT_final, bT_final, aC_final, bC_final, 100)
            if final_post_sup > 0.975:  # Success criterion for final analysis
                success_count += 1
                
        return success_count / num_sims
        
    def bayes_decision(self, ppos):
        """Make Bayesian decision based on PPoS"""
        if ppos >= self.THR_EFF:
            return "Success"
        elif ppos <= self.THR_FUT:
            return "Futility"
        else:
            return "Continue"
            
    def trajectory_demo(self, addT_example, addC_example, verbose=True):
        """
        Trajectory demonstration with preset data
        Shows complete trial process replay for training/validation
        
        Args:
            addT_example: List of treatment successes per batch
            addC_example: List of control successes per batch
            verbose: Whether to print detailed output
        
        Returns:
            dict: Trial results summary
        """
        self.reset_trial_data()
        
        if verbose:
            print(f"\n{'='*60}")
            print("BAYESIAN ADAPTIVE CLINICAL TRIAL SIMULATION")
            print(f"{'='*60}")
            print(f"Parameters:")
            print(f"  Batch size per group: {self.BLOCK}")
            print(f"  Maximum sample size per group: {self.N_MAX}")
            print(f"  Success threshold (PPoS): {self.THR_EFF}")
            print(f"  Futility threshold (PPoS): {self.THR_FUT}")
            print(f"  Monte Carlo samples: {self.MC_SAMP}")
            print(f"\nTrial Data:")
            print(f"  Treatment successes per batch: {addT_example}")
            print(f"  Control successes per batch: {addC_example}")
            print(f"\n{'='*60}")
            print("TRIAL PROGRESSION:")
            print(f"{'='*60}")
        
        # Process each batch
        for i, (t_success, c_success) in enumerate(zip(addT_example, addC_example)):
            if not self.trial_active:
                break
                
            # Update trial data
            self.batch_number += 1
            self.xT += t_success
            self.nT += self.BLOCK
            self.xC += c_success
            self.nC += self.BLOCK
            
            # Calculate PPoS
            current_ppos = self.calculate_ppos(self.xT, self.nT, self.xC, self.nC)
            self.ppos_history.append(current_ppos)
            
            # Make decision
            decision = self.bayes_decision(current_ppos)
            self.decision_history.append(decision)
            
            # Calculate success rates
            t_rate = (self.xT / self.nT * 100) if self.nT > 0 else 0
            c_rate = (self.xC / self.nC * 100) if self.nC > 0 else 0
            
            if verbose:
                print(f"\nBatch {self.batch_number}:")
                print(f"  New data: T={t_success}/{self.BLOCK} ({t_success/self.BLOCK*100:.1f}%), "
                      f"C={c_success}/{self.BLOCK} ({c_success/self.BLOCK*100:.1f}%)")
                print(f"  Cumulative: T={self.xT}/{self.nT} ({t_rate:.1f}%), "
                      f"C={self.xC}/{self.nC} ({c_rate:.1f}%)")
                print(f"  PPoS: {current_ppos:.4f}")
                print(f"  Decision: {decision}")
                
                if decision == "Success":
                    print(f"  ✓ EARLY SUCCESS: PPoS ({current_ppos:.4f}) ≥ {self.THR_EFF}")
                elif decision == "Futility":
                    print(f"  ✗ EARLY FUTILITY: PPoS ({current_ppos:.4f}) ≤ {self.THR_FUT}")
                else:
                    print(f"  → CONTINUE: {self.THR_FUT} < PPoS ({current_ppos:.4f}) < {self.THR_EFF}")
            
            # Check if trial should end
            if decision != "Continue":
                self.trial_active = False
                break
                
            # Check if maximum sample size reached
            if self.nT >= self.N_MAX:
                self.trial_active = False
                break
        
        # Final summary
        final_ppos = self.ppos_history[-1] if self.ppos_history else 0
        final_decision = self.decision_history[-1] if self.decision_history else "No decision"
        
        if verbose:
            print(f"\n{'='*60}")
            print("TRIAL SUMMARY:")
            print(f"{'='*60}")
            print(f"Final decision: {final_decision}")
            print(f"Total batches: {self.batch_number}")
            print(f"Final sample sizes: T={self.nT}, C={self.nC}")
            print(f"Final success rates: T={self.xT}/{self.nT} ({(self.xT/self.nT*100) if self.nT > 0 else 0:.1f}%), "
                  f"C={self.xC}/{self.nC} ({(self.xC/self.nC*100) if self.nC > 0 else 0:.1f}%)")
            print(f"Final PPoS: {final_ppos:.4f}")
            print(f"{'='*60}")
        
        return {
            'final_decision': final_decision,
            'batches': self.batch_number,
            'final_ppos': final_ppos,
            'treatment_success_rate': (self.xT / self.nT) if self.nT > 0 else 0,
            'control_success_rate': (self.xC / self.nC) if self.nC > 0 else 0,
            'ppos_history': self.ppos_history.copy(),
            'decision_history': self.decision_history.copy()
        }

def demo_scenarios():
    """Demonstrate different trial scenarios"""
    
    scenarios = {
        "Positive Trial": {
            "addT_example": [15, 16, 14, 17, 15, 16],
            "addC_example": [8, 9, 10, 8, 9, 7],
            "description": "Treatment clearly better - early success expected"
        },
        "Negative Trial": {
            "addT_example": [8, 7, 9, 8, 6, 9],
            "addC_example": [12, 13, 11, 14, 12, 13],
            "description": "Treatment not better - early futility expected"
        },
        "Borderline Trial": {
            "addT_example": [11, 12, 10, 13, 11, 12, 10, 11, 12, 13],
            "addC_example": [9, 10, 11, 8, 10, 9, 11, 10, 9, 8],
            "description": "Borderline case - trial continues to end"
        }
    }
    
    trial = BayesianTrialCore()
    
    for scenario_name, data in scenarios.items():
        print(f"\n\n{'#'*80}")
        print(f"SCENARIO: {scenario_name}")
        print(f"Description: {data['description']}")
        print(f"{'#'*80}")
        
        result = trial.trajectory_demo(data['addT_example'], data['addC_example'])
        
        print(f"\nScenario Result: {result['final_decision']} after {result['batches']} batches")
        print(f"Final PPoS: {result['final_ppos']:.4f}")

if __name__ == "__main__":
    demo_scenarios()
