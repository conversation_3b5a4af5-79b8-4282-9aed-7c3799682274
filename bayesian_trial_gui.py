import tkinter as tk
from tkinter import ttk, messagebox
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading

class BayesianTrialApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Bayesian Adaptive Clinical Trial Design")
        self.root.geometry("900x700")
        
        # Default parameters
        self.BLOCK = tk.IntVar(value=20)
        self.N_MAX = tk.IntVar(value=120)
        self.THR_EFF = tk.DoubleVar(value=0.95)
        self.THR_FUT = tk.DoubleVar(value=0.05)
        self.MC_SAMP = tk.IntVar(value=1000)
        
        # Trial data
        self.reset_trial_data()
        
        # Create UI
        self.create_ui()
        
    def reset_trial_data(self):
        """Reset all trial data to initial state"""
        self.xT = 0  # Treatment successes
        self.nT = 0  # Treatment total
        self.xC = 0  # Control successes
        self.nC = 0  # Control total
        self.ppos_history = []
        self.batch_number = 0
        self.trial_active = True
        self.decision_history = []
        
    def create_ui(self):
        """Create the user interface"""
        # Create notebook with tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Configuration
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Configuration")
        self.create_config_tab(config_frame)
        
        # Tab 2: Interactive Mode
        interactive_frame = ttk.Frame(notebook)
        notebook.add(interactive_frame, text="Interactive Mode")
        self.create_interactive_tab(interactive_frame)
        
        # Tab 3: Demo Mode
        demo_frame = ttk.Frame(notebook)
        notebook.add(demo_frame, text="Demo Mode")
        self.create_demo_tab(demo_frame)
        
    def create_config_tab(self, parent):
        """Create the configuration tab"""
        # Parameters frame
        param_frame = ttk.LabelFrame(parent, text="Trial Parameters")
        param_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # BLOCK
        ttk.Label(param_frame, text="Batch Size (per group):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.BLOCK, width=10).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="Number of patients enrolled in each batch per group").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # N_MAX
        ttk.Label(param_frame, text="Maximum Sample Size (per group):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.N_MAX, width=10).grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="Maximum number of patients per group").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # THR_EFF
        ttk.Label(param_frame, text="Success Threshold:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.THR_EFF, width=10).grid(row=2, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="PPoS threshold for early success (e.g., 0.95)").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        
        # THR_FUT
        ttk.Label(param_frame, text="Futility Threshold:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.THR_FUT, width=10).grid(row=3, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="PPoS threshold for early termination (e.g., 0.05)").grid(row=3, column=2, sticky=tk.W, padx=5, pady=5)
        
        # MC_SAMP
        ttk.Label(param_frame, text="Monte Carlo Samples:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.MC_SAMP, width=10).grid(row=4, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="Number of Monte Carlo samples for simulation").grid(row=4, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Save button
        ttk.Button(param_frame, text="Save Configuration", command=self.save_config).grid(row=5, column=0, columnspan=3, pady=10)
        
        # Description frame
        desc_frame = ttk.LabelFrame(parent, text="Description")
        desc_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        description = """
        This application implements a Bayesian adaptive clinical trial design with:
        
        - Real-time decision support based on Predictive Probability of Success (PPoS)
        - Early stopping rules for success or futility
        - Beta-Binomial conjugate prior for treatment effect
        - Monte Carlo simulation for posterior probability calculation
        
        Configure the parameters, then use either Interactive Mode to manually 
        input batch data or Demo Mode to see a pre-configured trial simulation.
        """
        
        desc_label = ttk.Label(desc_frame, text=description, wraplength=600, justify=tk.LEFT)
        desc_label.pack(padx=10, pady=10)
        
    def create_interactive_tab(self, parent):
        """Create the interactive mode tab"""
        # Input frame
        input_frame = ttk.LabelFrame(parent, text="Batch Data Input")
        input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Treatment group
        ttk.Label(input_frame, text="Treatment Group:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.treatment_success = tk.IntVar()
        ttk.Entry(input_frame, textvariable=self.treatment_success, width=10).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(input_frame, text="Number of successes in this batch").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Control group
        ttk.Label(input_frame, text="Control Group:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.control_success = tk.IntVar()
        ttk.Entry(input_frame, textvariable=self.control_success, width=10).grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(input_frame, text="Number of successes in this batch").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Submit button
        ttk.Button(input_frame, text="Submit Batch", command=self.process_batch).grid(row=2, column=0, columnspan=3, pady=10)
        
        # Reset button
        ttk.Button(input_frame, text="Reset Trial", command=self.reset_trial).grid(row=3, column=0, columnspan=3, pady=10)
        
        # Results frame
        results_frame = ttk.LabelFrame(parent, text="Trial Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Status display
        status_frame = ttk.Frame(results_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Batch:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.batch_label = ttk.Label(status_frame, text="0")
        self.batch_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Treatment:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.treatment_label = ttk.Label(status_frame, text="0/0 (0.0%)")
        self.treatment_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Control:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.control_label = ttk.Label(status_frame, text="0/0 (0.0%)")
        self.control_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Current PPoS:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.ppos_label = ttk.Label(status_frame, text="N/A")
        self.ppos_label.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Decision:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.decision_label = ttk.Label(status_frame, text="N/A")
        self.decision_label.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Plot frame
        plot_frame = ttk.Frame(results_frame)
        plot_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.fig, self.ax = plt.subplots(figsize=(6, 4))
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.ax.set_title("PPoS Over Time")
        self.ax.set_xlabel("Batch")
        self.ax.set_ylabel("PPoS")
        self.ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        self.ax.legend()
        self.canvas.draw()
        
    def create_demo_tab(self, parent):
        """Create the demo mode tab"""
        # Demo control frame
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Scenario selection
        ttk.Label(control_frame, text="Select Scenario:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.scenario = tk.StringVar(value="Positive Trial")
        scenario_combo = ttk.Combobox(control_frame, textvariable=self.scenario, 
                                      values=["Positive Trial", "Negative Trial", "Borderline Trial"])
        scenario_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # Start button
        ttk.Button(control_frame, text="Start Demo", command=self.start_demo).grid(row=0, column=2, padx=5, pady=5)
        
        # Demo status
        self.demo_status = ttk.Label(control_frame, text="Ready to start demo")
        self.demo_status.grid(row=1, column=0, columnspan=3, padx=5, pady=5)
        
        # Results frame
        demo_results_frame = ttk.LabelFrame(parent, text="Demo Results")
        demo_results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Status display
        demo_status_frame = ttk.Frame(demo_results_frame)
        demo_status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Batch:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_batch_label = ttk.Label(demo_status_frame, text="0")
        self.demo_batch_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Treatment:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_treatment_label = ttk.Label(demo_status_frame, text="0/0 (0.0%)")
        self.demo_treatment_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Control:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_control_label = ttk.Label(demo_status_frame, text="0/0 (0.0%)")
        self.demo_control_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Current PPoS:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_ppos_label = ttk.Label(demo_status_frame, text="N/A")
        self.demo_ppos_label.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Decision:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_decision_label = ttk.Label(demo_status_frame, text="N/A")
        self.demo_decision_label.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Plot frame
        demo_plot_frame = ttk.Frame(demo_results_frame)
        demo_plot_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.demo_fig, self.demo_ax = plt.subplots(figsize=(6, 4))
        self.demo_canvas = FigureCanvasTkAgg(self.demo_fig, master=demo_plot_frame)
        self.demo_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.demo_ax.set_title("PPoS Over Time (Demo)")
        self.demo_ax.set_xlabel("Batch")
        self.demo_ax.set_ylabel("PPoS")
        self.demo_ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.demo_ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        self.demo_ax.legend()
        self.demo_canvas.draw()
        
    def save_config(self):
        """Save the configuration parameters"""
        try:
            # Validate inputs
            if self.BLOCK.get() <= 0 or self.N_MAX.get() <= 0 or self.MC_SAMP.get() <= 0:
                raise ValueError("Block size, maximum sample size, and Monte Carlo samples must be positive integers")
            
            if not (0 < self.THR_FUT.get() < self.THR_EFF.get() < 1):
                raise ValueError("Thresholds must satisfy: 0 < Futility < Success < 1")
            
            # Update plot thresholds
            self.update_threshold_lines()
            
            messagebox.showinfo("Configuration", "Configuration saved successfully")
            
        except ValueError as e:
            messagebox.showerror("Invalid Input", str(e))
            
    def update_threshold_lines(self):
        """Update the threshold lines on both plots"""
        # Update interactive plot
        self.ax.clear()
        self.ax.set_title("PPoS Over Time")
        self.ax.set_xlabel("Batch")
        self.ax.set_ylabel("PPoS")
        self.ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        
        if self.ppos_history:
            batches = list(range(1, len(self.ppos_history) + 1))
            self.ax.plot(batches, self.ppos_history, 'bo-')
            
        self.ax.legend()
        self.canvas.draw()
        
        # Update demo plot
        self.demo_ax.clear()
        self.demo_ax.set_title("PPoS Over Time (Demo)")
        self.demo_ax.set_xlabel("Batch")
        self.demo_ax.set_ylabel("PPoS")
        self.demo_ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.demo_ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        self.demo_ax.legend()
        self.demo_canvas.draw()
        
    def process_batch(self):
        """Process a new batch of data from user input"""
        try:
            # Check if trial is still active
            if not self.trial_active:
                messagebox.showinfo("Trial Ended", "The trial has already ended. Please reset to start a new trial.")
                return
                
            # Validate inputs
            t_success = self.treatment_success.get()
            c_success = self.control_success.get()
            
            if t_success < 0 or t_success > self.BLOCK.get():
                raise ValueError(f"Treatment successes must be between 0 and {self.BLOCK.get()}")
                
            if c_success < 0 or c_success > self.BLOCK.get():
                raise ValueError(f"Control successes must be between 0 and {self.BLOCK.get()}")
                
            # Update trial data
            self.batch_number += 1
            self.xT += t_success
            self.nT += self.BLOCK.get()
            self.xC += c_success
            self.nC += self.BLOCK.get()
            
            # Calculate PPoS
            current_ppos = self.calculate_ppos(self.xT, self.nT, self.xC, self.nC)
            self.ppos_history.append(current_ppos)
            
            # Make decision
            decision = self.bayes_decision(current_ppos)
           