import tkinter as tk
from tkinter import ttk, messagebox
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading

class BayesianTrialApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Bayesian Adaptive Clinical Trial Design")
        self.root.geometry("900x700")
        
        # Default parameters
        self.BLOCK = tk.IntVar(value=20)
        self.N_MAX = tk.IntVar(value=120)
        self.THR_EFF = tk.DoubleVar(value=0.95)
        self.THR_FUT = tk.DoubleVar(value=0.05)
        self.MC_SAMP = tk.IntVar(value=1000)
        
        # Trial data
        self.reset_trial_data()
        
        # Create UI
        self.create_ui()
        
    def reset_trial_data(self):
        """Reset all trial data to initial state"""
        self.xT = 0  # Treatment successes
        self.nT = 0  # Treatment total
        self.xC = 0  # Control successes
        self.nC = 0  # Control total
        self.ppos_history = []
        self.batch_number = 0
        self.trial_active = True
        self.decision_history = []
        
    def create_ui(self):
        """Create the user interface"""
        # Create notebook with tabs
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Tab 1: Configuration
        config_frame = ttk.Frame(notebook)
        notebook.add(config_frame, text="Configuration")
        self.create_config_tab(config_frame)
        
        # Tab 2: Interactive Mode
        interactive_frame = ttk.Frame(notebook)
        notebook.add(interactive_frame, text="Interactive Mode")
        self.create_interactive_tab(interactive_frame)
        
        # Tab 3: Demo Mode
        demo_frame = ttk.Frame(notebook)
        notebook.add(demo_frame, text="Demo Mode")
        self.create_demo_tab(demo_frame)
        
    def create_config_tab(self, parent):
        """Create the configuration tab"""
        # Parameters frame
        param_frame = ttk.LabelFrame(parent, text="Trial Parameters")
        param_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # BLOCK
        ttk.Label(param_frame, text="Batch Size (per group):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.BLOCK, width=10).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="Number of patients enrolled in each batch per group").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # N_MAX
        ttk.Label(param_frame, text="Maximum Sample Size (per group):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.N_MAX, width=10).grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="Maximum number of patients per group").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # THR_EFF
        ttk.Label(param_frame, text="Success Threshold:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.THR_EFF, width=10).grid(row=2, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="PPoS threshold for early success (e.g., 0.95)").grid(row=2, column=2, sticky=tk.W, padx=5, pady=5)
        
        # THR_FUT
        ttk.Label(param_frame, text="Futility Threshold:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.THR_FUT, width=10).grid(row=3, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="PPoS threshold for early termination (e.g., 0.05)").grid(row=3, column=2, sticky=tk.W, padx=5, pady=5)
        
        # MC_SAMP
        ttk.Label(param_frame, text="Monte Carlo Samples:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Entry(param_frame, textvariable=self.MC_SAMP, width=10).grid(row=4, column=1, padx=5, pady=5)
        ttk.Label(param_frame, text="Number of Monte Carlo samples for simulation").grid(row=4, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Save button
        ttk.Button(param_frame, text="Save Configuration", command=self.save_config).grid(row=5, column=0, columnspan=3, pady=10)
        
        # Description frame
        desc_frame = ttk.LabelFrame(parent, text="Description")
        desc_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        description = """
        This application implements a Bayesian adaptive clinical trial design with:
        
        - Real-time decision support based on Predictive Probability of Success (PPoS)
        - Early stopping rules for success or futility
        - Beta-Binomial conjugate prior for treatment effect
        - Monte Carlo simulation for posterior probability calculation
        
        Configure the parameters, then use either Interactive Mode to manually 
        input batch data or Demo Mode to see a pre-configured trial simulation.
        """
        
        desc_label = ttk.Label(desc_frame, text=description, wraplength=600, justify=tk.LEFT)
        desc_label.pack(padx=10, pady=10)
        
    def create_interactive_tab(self, parent):
        """Create the interactive mode tab"""
        # Input frame
        input_frame = ttk.LabelFrame(parent, text="Batch Data Input")
        input_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Treatment group
        ttk.Label(input_frame, text="Treatment Group:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.treatment_success = tk.IntVar()
        ttk.Entry(input_frame, textvariable=self.treatment_success, width=10).grid(row=0, column=1, padx=5, pady=5)
        ttk.Label(input_frame, text="Number of successes in this batch").grid(row=0, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Control group
        ttk.Label(input_frame, text="Control Group:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.control_success = tk.IntVar()
        ttk.Entry(input_frame, textvariable=self.control_success, width=10).grid(row=1, column=1, padx=5, pady=5)
        ttk.Label(input_frame, text="Number of successes in this batch").grid(row=1, column=2, sticky=tk.W, padx=5, pady=5)
        
        # Submit button
        ttk.Button(input_frame, text="Submit Batch", command=self.process_batch).grid(row=2, column=0, columnspan=3, pady=10)
        
        # Reset button
        ttk.Button(input_frame, text="Reset Trial", command=self.reset_trial).grid(row=3, column=0, columnspan=3, pady=10)
        
        # Results frame
        results_frame = ttk.LabelFrame(parent, text="Trial Results")
        results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Status display
        status_frame = ttk.Frame(results_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Batch:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.batch_label = ttk.Label(status_frame, text="0")
        self.batch_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Treatment:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.treatment_label = ttk.Label(status_frame, text="0/0 (0.0%)")
        self.treatment_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Control:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.control_label = ttk.Label(status_frame, text="0/0 (0.0%)")
        self.control_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Current PPoS:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.ppos_label = ttk.Label(status_frame, text="N/A")
        self.ppos_label.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(status_frame, text="Decision:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.decision_label = ttk.Label(status_frame, text="N/A")
        self.decision_label.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Plot frame
        plot_frame = ttk.Frame(results_frame)
        plot_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.fig, self.ax = plt.subplots(figsize=(6, 4))
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.ax.set_title("PPoS Over Time")
        self.ax.set_xlabel("Batch")
        self.ax.set_ylabel("PPoS")
        self.ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        self.ax.legend()
        self.canvas.draw()
        
    def create_demo_tab(self, parent):
        """Create the demo mode tab"""
        # Demo control frame
        control_frame = ttk.Frame(parent)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Scenario selection
        ttk.Label(control_frame, text="Select Scenario:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.scenario = tk.StringVar(value="Positive Trial")
        scenario_combo = ttk.Combobox(control_frame, textvariable=self.scenario, 
                                      values=["Positive Trial", "Negative Trial", "Borderline Trial"])
        scenario_combo.grid(row=0, column=1, padx=5, pady=5)
        
        # Start button
        ttk.Button(control_frame, text="Start Demo", command=self.start_demo).grid(row=0, column=2, padx=5, pady=5)
        
        # Demo status
        self.demo_status = ttk.Label(control_frame, text="Ready to start demo")
        self.demo_status.grid(row=1, column=0, columnspan=3, padx=5, pady=5)
        
        # Results frame
        demo_results_frame = ttk.LabelFrame(parent, text="Demo Results")
        demo_results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Status display
        demo_status_frame = ttk.Frame(demo_results_frame)
        demo_status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Batch:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_batch_label = ttk.Label(demo_status_frame, text="0")
        self.demo_batch_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Treatment:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_treatment_label = ttk.Label(demo_status_frame, text="0/0 (0.0%)")
        self.demo_treatment_label.grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Control:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_control_label = ttk.Label(demo_status_frame, text="0/0 (0.0%)")
        self.demo_control_label.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Current PPoS:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_ppos_label = ttk.Label(demo_status_frame, text="N/A")
        self.demo_ppos_label.grid(row=3, column=1, sticky=tk.W, padx=5, pady=5)
        
        ttk.Label(demo_status_frame, text="Decision:").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.demo_decision_label = ttk.Label(demo_status_frame, text="N/A")
        self.demo_decision_label.grid(row=4, column=1, sticky=tk.W, padx=5, pady=5)
        
        # Plot frame
        demo_plot_frame = ttk.Frame(demo_results_frame)
        demo_plot_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.demo_fig, self.demo_ax = plt.subplots(figsize=(6, 4))
        self.demo_canvas = FigureCanvasTkAgg(self.demo_fig, master=demo_plot_frame)
        self.demo_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        self.demo_ax.set_title("PPoS Over Time (Demo)")
        self.demo_ax.set_xlabel("Batch")
        self.demo_ax.set_ylabel("PPoS")
        self.demo_ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.demo_ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        self.demo_ax.legend()
        self.demo_canvas.draw()
        
    def save_config(self):
        """Save the configuration parameters"""
        try:
            # Validate inputs
            if self.BLOCK.get() <= 0 or self.N_MAX.get() <= 0 or self.MC_SAMP.get() <= 0:
                raise ValueError("Block size, maximum sample size, and Monte Carlo samples must be positive integers")
            
            if not (0 < self.THR_FUT.get() < self.THR_EFF.get() < 1):
                raise ValueError("Thresholds must satisfy: 0 < Futility < Success < 1")
            
            # Update plot thresholds
            self.update_threshold_lines()
            
            messagebox.showinfo("Configuration", "Configuration saved successfully")
            
        except ValueError as e:
            messagebox.showerror("Invalid Input", str(e))
            
    def update_threshold_lines(self):
        """Update the threshold lines on both plots"""
        # Update interactive plot
        self.ax.clear()
        self.ax.set_title("PPoS Over Time")
        self.ax.set_xlabel("Batch")
        self.ax.set_ylabel("PPoS")
        self.ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        
        if self.ppos_history:
            batches = list(range(1, len(self.ppos_history) + 1))
            self.ax.plot(batches, self.ppos_history, 'bo-')
            
        self.ax.legend()
        self.canvas.draw()
        
        # Update demo plot
        self.demo_ax.clear()
        self.demo_ax.set_title("PPoS Over Time (Demo)")
        self.demo_ax.set_xlabel("Batch")
        self.demo_ax.set_ylabel("PPoS")
        self.demo_ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.demo_ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        self.demo_ax.legend()
        self.demo_canvas.draw()
        
    def process_batch(self):
        """Process a new batch of data from user input"""
        try:
            # Check if trial is still active
            if not self.trial_active:
                messagebox.showinfo("Trial Ended", "The trial has already ended. Please reset to start a new trial.")
                return
                
            # Validate inputs
            t_success = self.treatment_success.get()
            c_success = self.control_success.get()
            
            if t_success < 0 or t_success > self.BLOCK.get():
                raise ValueError(f"Treatment successes must be between 0 and {self.BLOCK.get()}")
                
            if c_success < 0 or c_success > self.BLOCK.get():
                raise ValueError(f"Control successes must be between 0 and {self.BLOCK.get()}")
                
            # Update trial data
            self.batch_number += 1
            self.xT += t_success
            self.nT += self.BLOCK.get()
            self.xC += c_success
            self.nC += self.BLOCK.get()
            
            # Calculate PPoS
            current_ppos = self.calculate_ppos(self.xT, self.nT, self.xC, self.nC)
            self.ppos_history.append(current_ppos)
            
            # Make decision
            decision = self.bayes_decision(current_ppos)
            self.decision_history.append(decision)

            # Update display
            self.update_display()

            # Check if trial should end
            if decision != "Continue":
                self.trial_active = False
                self.demo_status.config(text=f"Trial ended: {decision}")

            # Clear input fields
            self.treatment_success.set(0)
            self.control_success.set(0)

        except ValueError as e:
            messagebox.showerror("Invalid Input", str(e))

    def reset_trial(self):
        """Reset the trial to initial state"""
        self.reset_trial_data()
        self.update_display()
        self.update_threshold_lines()
        messagebox.showinfo("Trial Reset", "Trial has been reset to initial state")

    def post_sup(self, aT, bT, aC, bC, S=None):
        """
        Calculate posterior superiority probability P(pT > pC | data)
        using Beta-Binomial conjugate prior with Monte Carlo simulation

        Args:
            aT, bT: Beta parameters for treatment group (successes + 1, failures + 1)
            aC, bC: Beta parameters for control group (successes + 1, failures + 1)
            S: Number of Monte Carlo samples

        Returns:
            Probability that treatment success rate > control success rate
        """
        if S is None:
            S = self.MC_SAMP.get()

        # Sample from Beta posterior distributions
        pT_samples = np.random.beta(aT, bT, S)
        pC_samples = np.random.beta(aC, bC, S)

        # Calculate proportion where pT > pC
        return np.mean(pT_samples > pC_samples)

    def calculate_ppos(self, xT, nT, xC, nC):
        """
        Calculate Predictive Probability of Success (PPoS)

        Args:
            xT: Treatment successes so far
            nT: Treatment total so far
            xC: Control successes so far
            nC: Control total so far

        Returns:
            PPoS: Probability of trial success if continued to maximum sample size
        """
        if nT == 0 or nC == 0:
            return 0.5  # No data yet

        # Remaining patients per group
        remaining_T = self.N_MAX.get() - nT
        remaining_C = self.N_MAX.get() - nC

        if remaining_T <= 0 or remaining_C <= 0:
            # Already at maximum, calculate final posterior
            aT = xT + 1  # Beta prior: alpha = 1
            bT = nT - xT + 1  # Beta prior: beta = 1
            aC = xC + 1
            bC = nC - xC + 1
            return self.post_sup(aT, bT, aC, bC)

        # Monte Carlo simulation for future outcomes
        num_sims = 1000
        success_count = 0

        for _ in range(num_sims):
            # Current posterior parameters
            aT_curr = xT + 1
            bT_curr = nT - xT + 1
            aC_curr = xC + 1
            bC_curr = nC - xC + 1

            # Sample current success rates from posterior
            pT_curr = np.random.beta(aT_curr, bT_curr)
            pC_curr = np.random.beta(aC_curr, bC_curr)

            # Simulate future outcomes
            future_xT = np.random.binomial(remaining_T, pT_curr)
            future_xC = np.random.binomial(remaining_C, pC_curr)

            # Final counts
            final_xT = xT + future_xT
            final_nT = nT + remaining_T
            final_xC = xC + future_xC
            final_nC = nC + remaining_C

            # Final posterior parameters
            aT_final = final_xT + 1
            bT_final = final_nT - final_xT + 1
            aC_final = final_xC + 1
            bC_final = final_nC - final_xC + 1

            # Check if this simulation leads to success
            final_post_sup = self.post_sup(aT_final, bT_final, aC_final, bC_final, 100)
            if final_post_sup > 0.975:  # Success criterion for final analysis
                success_count += 1

        return success_count / num_sims

    def bayes_decision(self, ppos):
        """
        Make Bayesian decision based on PPoS

        Args:
            ppos: Current Predictive Probability of Success

        Returns:
            Decision string: "Success", "Futility", or "Continue"
        """
        if ppos >= self.THR_EFF.get():
            return "Success"
        elif ppos <= self.THR_FUT.get():
            return "Futility"
        else:
            return "Continue"

    def update_display(self):
        """Update all display elements with current trial data"""
        # Calculate success rates
        t_rate = (self.xT / self.nT * 100) if self.nT > 0 else 0
        c_rate = (self.xC / self.nC * 100) if self.nC > 0 else 0

        # Update interactive tab
        self.batch_label.config(text=str(self.batch_number))
        self.treatment_label.config(text=f"{self.xT}/{self.nT} ({t_rate:.1f}%)")
        self.control_label.config(text=f"{self.xC}/{self.nC} ({c_rate:.1f}%)")

        if self.ppos_history:
            current_ppos = self.ppos_history[-1]
            self.ppos_label.config(text=f"{current_ppos:.3f}")

            if self.decision_history:
                current_decision = self.decision_history[-1]
                self.decision_label.config(text=current_decision)

                # Color code the decision
                if current_decision == "Success":
                    self.decision_label.config(foreground="green")
                elif current_decision == "Futility":
                    self.decision_label.config(foreground="red")
                else:
                    self.decision_label.config(foreground="black")

        # Update demo tab (same data)
        self.demo_batch_label.config(text=str(self.batch_number))
        self.demo_treatment_label.config(text=f"{self.xT}/{self.nT} ({t_rate:.1f}%)")
        self.demo_control_label.config(text=f"{self.xC}/{self.nC} ({c_rate:.1f}%)")

        if self.ppos_history:
            self.demo_ppos_label.config(text=f"{current_ppos:.3f}")
            if self.decision_history:
                self.demo_decision_label.config(text=current_decision)

                # Color code the decision
                if current_decision == "Success":
                    self.demo_decision_label.config(foreground="green")
                elif current_decision == "Futility":
                    self.demo_decision_label.config(foreground="red")
                else:
                    self.demo_decision_label.config(foreground="black")

        # Update plots
        self.update_plots()

    def update_plots(self):
        """Update both interactive and demo plots"""
        if not self.ppos_history:
            return

        batches = list(range(1, len(self.ppos_history) + 1))

        # Update interactive plot
        self.ax.clear()
        self.ax.set_title("PPoS Over Time")
        self.ax.set_xlabel("Batch")
        self.ax.set_ylabel("PPoS")
        self.ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        self.ax.plot(batches, self.ppos_history, 'bo-', label="PPoS")
        self.ax.set_ylim(0, 1)
        self.ax.legend()
        self.canvas.draw()

        # Update demo plot
        self.demo_ax.clear()
        self.demo_ax.set_title("PPoS Over Time (Demo)")
        self.demo_ax.set_xlabel("Batch")
        self.demo_ax.set_ylabel("PPoS")
        self.demo_ax.axhline(y=self.THR_EFF.get(), color='g', linestyle='--', label=f"Success ({self.THR_EFF.get()})")
        self.demo_ax.axhline(y=self.THR_FUT.get(), color='r', linestyle='--', label=f"Futility ({self.THR_FUT.get()})")
        self.demo_ax.plot(batches, self.ppos_history, 'bo-', label="PPoS")
        self.demo_ax.set_ylim(0, 1)
        self.demo_ax.legend()
        self.demo_canvas.draw()

    def start_demo(self):
        """Start the demo mode with predefined scenarios"""
        scenario = self.scenario.get()

        # Reset trial data
        self.reset_trial_data()

        # Define scenario data
        if scenario == "Positive Trial":
            # Treatment is clearly better - early success expected
            addT_example = [15, 16, 14, 17, 15, 16]  # High success rates
            addC_example = [8, 9, 10, 8, 9, 7]      # Lower success rates
            self.demo_status.config(text="Running Positive Trial Demo...")
        elif scenario == "Negative Trial":
            # Treatment is not better - early futility expected
            addT_example = [8, 7, 9, 8, 6, 9]       # Low success rates
            addC_example = [12, 13, 11, 14, 12, 13] # Higher success rates
            self.demo_status.config(text="Running Negative Trial Demo...")
        else:  # Borderline Trial
            # Borderline case - trial continues to end
            addT_example = [11, 12, 10, 13, 11, 12, 10, 11, 12, 13]  # Moderate success
            addC_example = [9, 10, 11, 8, 10, 9, 11, 10, 9, 8]      # Slightly lower
            self.demo_status.config(text="Running Borderline Trial Demo...")

        # Start trajectory demo in a separate thread to avoid blocking UI
        demo_thread = threading.Thread(target=self.trajectory_demo,
                                      args=(addT_example, addC_example))
        demo_thread.daemon = True
        demo_thread.start()

    def trajectory_demo(self, addT_example, addC_example):
        """
        Trajectory demonstration with preset data
        Shows complete trial process replay for training/validation

        Args:
            addT_example: List of treatment successes per batch
            addC_example: List of control successes per batch
        """
        try:
            # Ensure we start fresh
            self.root.after(0, self.reset_trial_data)
            self.root.after(0, self.update_display)

            # Process each batch with delay for visualization
            for i, (t_success, c_success) in enumerate(zip(addT_example, addC_example)):
                # Wait between batches for demo effect
                import time
                time.sleep(2)

                # Check if trial is still active
                if not self.trial_active:
                    break

                # Validate batch data
                if t_success < 0 or t_success > self.BLOCK.get():
                    self.root.after(0, lambda: messagebox.showerror(
                        "Demo Error", f"Invalid treatment data in batch {i+1}: {t_success}"))
                    return

                if c_success < 0 or c_success > self.BLOCK.get():
                    self.root.after(0, lambda: messagebox.showerror(
                        "Demo Error", f"Invalid control data in batch {i+1}: {c_success}"))
                    return

                # Update trial data
                self.batch_number += 1
                self.xT += t_success
                self.nT += self.BLOCK.get()
                self.xC += c_success
                self.nC += self.BLOCK.get()

                # Calculate PPoS
                current_ppos = self.calculate_ppos(self.xT, self.nT, self.xC, self.nC)
                self.ppos_history.append(current_ppos)

                # Make decision
                decision = self.bayes_decision(current_ppos)
                self.decision_history.append(decision)

                # Update display in main thread
                self.root.after(0, self.update_display)

                # Update status
                status_text = f"Batch {self.batch_number}: T={t_success}/{self.BLOCK.get()}, C={c_success}/{self.BLOCK.get()}, PPoS={current_ppos:.3f}, Decision={decision}"
                self.root.after(0, lambda text=status_text: self.demo_status.config(text=text))

                # Check if trial should end
                if decision != "Continue":
                    self.trial_active = False
                    final_status = f"Demo completed: {decision} after {self.batch_number} batches"
                    self.root.after(0, lambda text=final_status: self.demo_status.config(text=text))
                    break

                # Check if maximum sample size reached
                if self.nT >= self.N_MAX.get():
                    self.trial_active = False
                    final_status = f"Demo completed: Maximum sample size reached after {self.batch_number} batches"
                    self.root.after(0, lambda text=final_status: self.demo_status.config(text=text))
                    break

            # Final summary
            if self.trial_active:  # Completed all batches without early stopping
                final_ppos = self.ppos_history[-1] if self.ppos_history else 0
                final_decision = "Success" if final_ppos > 0.5 else "Futility"
                final_status = f"Demo completed: {final_decision} (Final PPoS: {final_ppos:.3f})"
                self.root.after(0, lambda text=final_status: self.demo_status.config(text=text))

        except Exception as e:
            error_msg = f"Demo error: {str(e)}"
            self.root.after(0, lambda: messagebox.showerror("Demo Error", error_msg))
            self.root.after(0, lambda: self.demo_status.config(text="Demo failed"))


# Main application entry point
if __name__ == "__main__":
    root = tk.Tk()
    app = BayesianTrialApp(root)
    root.mainloop()