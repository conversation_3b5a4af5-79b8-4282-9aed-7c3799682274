#!/usr/bin/env python3
"""
Test script for the trajectory_demo functionality
This demonstrates how to use the trajectory_demo function with example data
"""

import tkinter as tk
from bayesian_trial_gui import BayesianTrialApp
import threading
import time

def test_trajectory_demo():
    """Test the trajectory_demo function with predefined data"""
    
    # Create the application
    root = tk.Tk()
    app = BayesianTrialApp(root)
    
    # Example data for trajectory demo
    # These represent successes per batch for treatment and control groups
    addT_example = [15, 16, 14, 17, 15, 16]  # High success rates (positive trial)
    addC_example = [8, 9, 10, 8, 9, 7]       # Lower success rates
    
    print("Starting trajectory demo with example data:")
    print(f"Treatment successes per batch: {addT_example}")
    print(f"Control successes per batch: {addC_example}")
    print(f"Batch size per group: {app.BLOCK.get()}")
    print(f"Success threshold: {app.THR_EFF.get()}")
    print(f"Futility threshold: {app.THR_FUT.get()}")
    print("\nThis should demonstrate an early success scenario...")
    
    # Start the trajectory demo after a short delay
    def start_demo_delayed():
        time.sleep(1)  # Give the GUI time to initialize
        app.trajectory_demo(addT_example, addC_example)
    
    # Run demo in background thread
    demo_thread = threading.Thread(target=start_demo_delayed)
    demo_thread.daemon = True
    demo_thread.start()
    
    # Show the GUI
    print("\nGUI window should open. The demo will run automatically.")
    print("You can also manually test by:")
    print("1. Going to the 'Demo Mode' tab")
    print("2. Selecting a scenario (Positive/Negative/Borderline Trial)")
    print("3. Clicking 'Start Demo'")
    print("\nClose the window to exit.")
    
    root.mainloop()

def test_different_scenarios():
    """Test different trial scenarios"""
    
    scenarios = {
        "Positive Trial": {
            "addT_example": [15, 16, 14, 17, 15, 16],
            "addC_example": [8, 9, 10, 8, 9, 7],
            "description": "Treatment clearly better - early success expected"
        },
        "Negative Trial": {
            "addT_example": [8, 7, 9, 8, 6, 9],
            "addC_example": [12, 13, 11, 14, 12, 13],
            "description": "Treatment not better - early futility expected"
        },
        "Borderline Trial": {
            "addT_example": [11, 12, 10, 13, 11, 12, 10, 11, 12, 13],
            "addC_example": [9, 10, 11, 8, 10, 9, 11, 10, 9, 8],
            "description": "Borderline case - trial continues to end"
        }
    }
    
    print("Available trajectory demo scenarios:")
    print("=" * 50)
    
    for scenario_name, data in scenarios.items():
        print(f"\n{scenario_name}:")
        print(f"  Description: {data['description']}")
        print(f"  Treatment data: {data['addT_example']}")
        print(f"  Control data: {data['addC_example']}")
        
        # Calculate expected rates
        t_total = len(data['addT_example']) * 20  # 20 per batch
        c_total = len(data['addC_example']) * 20
        t_success_rate = sum(data['addT_example']) / t_total * 100
        c_success_rate = sum(data['addC_example']) / c_total * 100
        
        print(f"  Expected treatment success rate: {t_success_rate:.1f}%")
        print(f"  Expected control success rate: {c_success_rate:.1f}%")

if __name__ == "__main__":
    print("Bayesian Trial GUI - Trajectory Demo Test")
    print("=" * 45)
    
    # Show available scenarios
    test_different_scenarios()
    
    print("\n" + "=" * 45)
    print("Starting interactive demo...")
    
    # Run the interactive demo
    test_trajectory_demo()
